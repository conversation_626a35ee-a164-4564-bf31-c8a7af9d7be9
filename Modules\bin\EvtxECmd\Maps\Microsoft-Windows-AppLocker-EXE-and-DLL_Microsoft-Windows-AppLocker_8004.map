Author: <PERSON>
Description: An executable was prevented from running.
EventId: 8004
Channel: Microsoft-Windows-AppLocker/EXE and DLL
Provider: Microsoft-Windows-AppLocker
Maps:
  -
    Property: ExecutableInfo
    PropertyValue: "%FilePath%"
    Values:
      -
        Name: FilePath
        Value: "/Event/UserData/RuleAndFileData/FilePath"
  -
    Property: PayloadData1
    PropertyValue: "%FileHash%"
    Values:
      -
        Name: FileHash
        Value: "/Event/UserData/RuleAndFileData/FileHash"
  -
    Property: PayloadData2
    PropertyValue: "%Fqbn%"
    Values:
      -
        Name: Fqbn
        Value: "/Event/UserData/RuleAndFileData/Fqbn"
  -
    Property: PayloadData3
    PropertyValue: "TargetUser"
    Values:
      -
        Name: TargetUser
        Value: "/Event/UserData/RuleAndFileData/TargetUser"
  -
    Property: PayloadData4
    PropertyValue: "%TargetLogonId%"
    Values:
      -
        Name: TargetLogonId
        Value: "/Event/UserData/RuleAndFileData/TargetLogonId"
  -
    Property: PayloadData5
    PropertyValue: "%FullFilePath%"
    Values:
      -
        Name: FullFilePath
        Value: "/Event/UserData/RuleAndFileData/FullFilePath"

# Documentation:
# https://docs.microsoft.com/en-us/windows/security/threat-protection/windows-defender-application-control/applocker/using-event-viewer-with-applocker
#
# Example Event Data:
# <Event>
#   <System>
#     <Provider Name="Microsoft-Windows-AppLocker" Guid="{cbda4dbf-8d5d-4f69-9578-be14aa540d22}" />
#     <EventID>8004</EventID>
#     <Version>0</Version>
#     <Level>2</Level>
#     <Task>0</Task>
#     <Opcode>0</Opcode>
#     <Keywords>0x8000000000000000</Keywords>
#     <TimeCreated SystemTime="2019-12-03T18:57:53.108295900Z" />
#     <EventRecordID>128260</EventRecordID>
#     <Correlation />
#     <Execution ProcessID="11716" ThreadID="3576" />
#     <Channel>Microsoft-Windows-AppLocker/EXE and DLL</Channel>
#     <Computer>Thunder.cloud.corp.com</Computer>
#     <Security UserID="[SID]" />
#   </System>
#   <UserData>
#     <RuleAndFileData xmlns="http://schemas.microsoft.com/schemas/event/Microsoft.Windows/1.0.0.0">
#       <PolicyNameLength>3</PolicyNameLength>
#       <PolicyName>EXE</PolicyName>
#       <RuleId>{149ab2be-bd65-4dd5-91ef-1ad29adc0f2f}</RuleId>
#       <RuleNameLength>34</RuleNameLength>
#       <RuleName>%OSDRIVE%\Users\thor\Documents\*</RuleName>
#       <RuleSddlLength>81</RuleSddlLength>
#       <RuleSddl>D:(XD;;FX;;;S-1-1-0;(APPID://PATH Contains "%OSDRIVE%\USERS\THOR\DOCUMENTS\*"))</RuleSddl>
#       <TargetUser>[SID]</TargetUser>
#       <TargetProcessId>2232</TargetProcessId>
#       <FilePathLength>45</FilePathLength>
#       <FilePath>%OSDRIVE%\USERS\THOR\DOCUMENTS\BGINFO64.EXE</FilePath>
#       <FileHashLength>32</FileHashLength>
#       <FileHash>078D5C1C46CF44C4113AB2D3D40F93EA5D9617E71B86DA7FF8292C328207FB2D</FileHash>
#       <FqbnLength>82</FqbnLength>
#       <Fqbn>O=MICROSOFT CORPORATION, L=REDMOND, S=WASHINGTON, C=US\BGINFO\BGINFO.EXE\4.28.0.00</Fqbn>
#       <TargetLogonId>0x3b3057</TargetLogonId>
#       <FullFilePathLength>38</FullFilePathLength>
#       <FullFilePath>C:\Users\<USER>\Documents\Bginfo64.exe</FullFilePath>
#     </RuleAndFileData>
#   </UserData>
# </Event>
