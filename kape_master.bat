@echo off
setlocal enabledelayedexpansion
:start

:::-------------------------------------------------------------------------------------------
:::     __ __ ___    ____  ______   __________  __    __    __________________________  _   __
:::    / //_//   |  / __ \/ ____/  / ____/ __ \/ /   / /   / ____/ ____/_  __/  _/ __ \/ | / /
:::   / ,<  / /| | / /_/ / __/    / /   / / / / /   / /   / __/ / /     / /  / // / / /  |/ / 
:::  / /| |/ ___ |/ ____/ /___   / /___/ /_/ / /___/ /___/ /___/ /___  / / _/ // /_/ / /|  /  
::: /_/ |_/_/  |_/_/   /_____/   \____/\____/_____/_____/_____/\____/ /_/ /___/\____/_/ |_/   
:::-------------------------------------------------------------------------------------------

echo ========================================
echo         Select the Drive.
echo ========================================
echo.
echo Please select an option:
echo 1. Select custom drive letter
echo 2. Use default (C: drive)
echo.

:input_loop
set /p choice="Enter your choice (1 or 2): "

if "%choice%"=="1" goto custom_drive
if "%choice%"=="2" goto default_drive
echo Invalid choice. Please enter 1 or 2.
goto input_loop

:custom_drive
echo.
set /p drive_letter="Enter drive letter (e.g., D, E, F): "

REM Remove any colon if user entered it
set drive_letter=%drive_letter::=%

REM Validate drive letter (basic check)
if "%drive_letter%"=="" (
    echo Error: No drive letter entered.
    goto custom_drive
)

REM Check if it's a single character
if not "%drive_letter:~1%"=="" (
    echo Error: Please enter only a single drive letter.
    goto custom_drive
)

REM Convert to uppercase
for %%i in (A B C D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if /i "%drive_letter%"=="%%i" set drive_letter=%%i
)

REM Check if drive exists
if not exist %drive_letter%:\ (
    echo Error: Drive %drive_letter%: does not exist or is not accessible.
    echo.
    goto custom_drive
)

set selected_drive=%drive_letter%:
echo Selected drive: %selected_drive%
goto execute_kape

:default_drive
set selected_drive=C:
echo Using default drive: %selected_drive%
goto execute_kape

:execute_kape
echo.
echo Executing KAPE with source drive: %selected_drive%
echo.

REM Call the second script with the selected drive
call kape_executor.bat "%selected_drive%"

pause
