Author: <PERSON><PERSON> Moore
Description: An executable was allowed to run
EventId: 8002
Channel: Microsoft-Windows-AppLocker/EXE and DLL
Maps: 
  - 
    Property: ExecutableInfo
    PropertyValue: "%FilePath%"
    Values: 
      - 
        Name: FilePath
        Value: "/Event/UserData/RuleAndFileData/FilePath"
  - 
    Property: PayloadData1
    PropertyValue: "%FileHash%"
    Values: 
      - 
        Name: FileHash
        Value: "/Event/UserData/RuleAndFileData/FileHash"
  - 
    Property: PayloadData2
    PropertyValue: "%Fqbn%"
    Values: 
      - 
        Name: Fqbn
        Value: "/Event/UserData/RuleAndFileData/Fqbn"
  - 
    Property: PayloadData3
    PropertyValue: "TargetUser"
    Values: 
      - 
        Name: TargetUser
        Value: "/Event/UserData/RuleAndFileData/TargetUser"
  - 
    Property: PayloadData4
    PropertyValue: "%TargetLogonId%"
    Values: 
      - 
        Name: TargetLogonId
        Value: "/Event/UserData/RuleAndFileData/TargetLogonId"
  - 
    Property: PayloadData5
    PropertyValue: "%FullFilePath%"
    Values: 
      - 
        Name: FullFilePath
        Value: "/Event/UserData/RuleAndFileData/FullFilePath"
        
# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

# <Event>
  # <System>
    # <Provider Name="Microsoft-Windows-AppLocker" Guid="xxxxxxxxxxxxxxx" />
    # <EventID>8002</EventID>
    # <Version>0</Version>
    # <Level>4</Level>
    # <Task>0</Task>
    # <Opcode>0</Opcode>
    # <Keywords>0x8000000000000000</Keywords>
    # <TimeCreated SystemTime="2019-11-07 22:15:09.5660628" />
    # <EventRecordID>69257</EventRecordID>
    # <Correlation />
    # <Execution ProcessID="2968" ThreadID="4456" />
    # <Channel>Microsoft-Windows-AppLocker/EXE and DLL</Channel>
    # <Computer>xxxxxxxxxxx</Computer>
    # <Security UserID="xxxxxxxxxxxxxxx" />
  # </System>
  # <UserData>
    # <RuleAndFileData>
      # <PolicyNameLength>3</PolicyNameLength>
      # <PolicyName>EXE</PolicyName>
      # <RuleId>4bad95f0-**************-a693d3506806</RuleId>
      # <RuleNameLength>24</RuleNameLength>
      # <RuleName>(Default Rule) All Exe's</RuleName>
      # <RuleSddlLength>48</RuleSddlLength>
      # <RuleSddl>D:(XA;;FX;;;S-1-1-0;(APPID://PATH Contains "*"))</RuleSddl>
      # <TargetUser>xxxxxx</TargetUser>
      # <TargetProcessId>6348</TargetProcessId>
      # <FilePathLength>22</FilePathLength>
      # <FilePath>%SYSTEM32%\RDPCLIP.EXE</FilePath>
      # <FileHashLength>32</FileHashLength>
      # <FileHash>10-67-8D-A8-38-A4-B1-65-60-61-15-36-79-FD-C4-41-3B-26-C4-D5-ED-F2-C1-3A-B7-66-1B-EB-4E-ED-60-81</FileHash>
      # <FqbnLength>118</FqbnLength>
      # <Fqbn>O=MICROSOFT CORPORATION, L=REDMOND, S=WASHINGTON, C=US\MICROSOFT® WINDOWS® OPERATING SYSTEM\RDPCLIP.EXE\6.3.9600.19393</Fqbn>
    # </RuleAndFileData>
  # </UserData>
# </Event>