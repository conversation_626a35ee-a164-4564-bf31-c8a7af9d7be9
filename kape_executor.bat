@echo off
setlocal enabledelayedexpansion

REM Get the source drive from parameter
set source_drive=%~1

REM Remove quotes if present
set source_drive=%source_drive:"=%

REM Get current date and time in YYYYMMDDHHMMSS format
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,14%"

REM Get computer hostname
set "hostname=%COMPUTERNAME%"

REM Extract drive letter (remove colon)
set "drive_letter=%source_drive::=%"

REM Convert drive letter to lowercase for folder name
for %%i in (a b c d e f g h i j k l m n o p q r s t u v w x y z) do (
    if /i "%drive_letter%"=="%%i" set drive_letter_lower=%%i
)

REM Get current KAPE directory
set "kape_dir=%~dp0"
set "kape_dir=%kape_dir:~0,-1%"

REM Create folder name: timestamp_hostname_driveletter_
set "folder_name=%timestamp%_%hostname%_%drive_letter_lower%_"

REM Create destination path
set "dest_path=%kape_dir%\%folder_name%"

REM Create the destination directory if it doesn't exist
if not exist "%dest_path%" mkdir "%dest_path%"

REM Create the KAPE target output subfolder
set "kape_output_folder=%folder_name%#KAPE_Target_Output"
set "kape_output_path=%dest_path%\%kape_output_folder%"

if not exist "%kape_output_path%" mkdir "%kape_output_path%"

echo ========================================
echo         KAPE Execution Details
echo ========================================
echo Source Drive: %source_drive%
echo Destination: %dest_path%
echo KAPE Output: %kape_output_path%
echo Timestamp: %timestamp%
echo Hostname: %hostname%
echo ========================================
echo.

REM Execute KAPE command
echo Executing KAPE...
echo.

"%kape_dir%\kape.exe" --tsource %source_drive% --tdest "%kape_output_path%" --target EventLogs,RegistryHivesUser --zip %folder_name% --gui

REM Check if KAPE executed successfully
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo KAPE execution completed successfully!
    echo Creating main folder ZIP archive...
    echo ========================================

    REM Create ZIP of the main output folder
    powershell -command "Compress-Archive -Path '%dest_path%' -DestinationPath '%dest_path%.zip' -Force"

    if %errorlevel% equ 0 (
        echo Main folder ZIP created: %dest_path%.zip
        echo.
        echo Cleaning up unzipped folder...
        rmdir /s /q "%dest_path%"
        echo ========================================
        echo Process completed successfully!
        echo Final output: %dest_path%.zip
        echo ========================================
    ) else (
        echo Warning: Failed to create main folder ZIP
        echo Output location: %dest_path%
    )
) else (
    echo.
    echo ========================================
    echo KAPE execution failed with error code: %errorlevel%
    echo ========================================
)

echo.
echo Script completed. Exiting...
