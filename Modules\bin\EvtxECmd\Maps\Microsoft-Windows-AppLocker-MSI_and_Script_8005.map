Author: <PERSON><PERSON>\Troy Larson
Description: A script or MSI was allowed to run.
EventId: 8005
Channel: Microsoft-Windows-AppLocker/MSI and Script
Maps: 
  - 
    Property: ExecutableInfo
    PropertyValue: "%FilePath%"
    Values: 
      - 
        Name: FilePath
        Value: "/Event/UserData/RuleAndFileData/FilePath"
  - 
    Property: PayloadData1
    PropertyValue: "%FileHash%"
    Values: 
      - 
        Name: FileHash
        Value: "/Event/UserData/RuleAndFileData/FileHash"
  - 
    Property: PayloadData2
    PropertyValue: "%Fqbn%"
    Values: 
      - 
        Name: Fqbn
        Value: "/Event/UserData/RuleAndFileData/Fqbn"
  - 
    Property: PayloadData3
    PropertyValue: "TargetUser"
    Values: 
      - 
        Name: TargetUser
        Value: "/Event/UserData/RuleAndFileData/TargetUser"
  - 
    Property: PayloadData4
    PropertyValue: "%TargetLogonId%"
    Values: 
      - 
        Name: TargetLogonId
        Value: "/Event/UserData/RuleAndFileData/TargetLogonId"
  - 
    Property: PayloadData5
    PropertyValue: "%FullFilePath%"
    Values: 
      - 
        Name: FullFilePath
        Value: "/Event/UserData/RuleAndFileData/FullFilePath"

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

# Sample event:
# <Event>
#   <System>
#     <Provider Name="Microsoft-Windows-AppLocker" Guid="{cbda4dbf-8d5d-4f69-9578-be14aa540d22}" /> 
#     <EventID>8005</EventID> 
#     <Version>0</Version> 
#     <Level>4</Level> 
#     <Task>0</Task> 
#     <Opcode>0</Opcode> 
#     <Keywords>0x4000000000000000</Keywords> 
#     <TimeCreated SystemTime="2019-12-03T01:37:17.856576200Z" /> 
#     <EventRecordID>1</EventRecordID> 
#     <Correlation ActivityID="{c8287304-a3c6-0001-fb01-37c8c6a3d501}" /> 
#     <Execution ProcessID="19768" ThreadID="17140" /> 
#     <Channel>Microsoft-Windows-AppLocker/MSI and Script</Channel> 
#     <Computer>Thunder.cloud.corp.com</Computer> 
#     <Security UserID="[SID]" /> 
#   </System>
#   <UserData>
#     <RuleAndFileData xmlns="http://schemas.microsoft.com/schemas/event/Microsoft.Windows/*******">
#       <PolicyNameLength>6</PolicyNameLength> 
#       <PolicyName>SCRIPT</PolicyName> 
#       <RuleId>{ed97d0cb-15ff-430f-b82c-8d7832957725}</RuleId> 
#       <RuleNameLength>26</RuleNameLength> 
#       <RuleName>(Default Rule) All scripts</RuleName> 
#       <RuleSddlLength>53</RuleSddlLength> 
#       <RuleSddl>D:(XA;;FX;;;S-1-5-32-544;(APPID://PATH Contains "*"))</RuleSddl> 
#       <TargetUser>[SID]</TargetUser> 
#       <TargetProcessId>19768</TargetProcessId> 
#       <FilePathLength>71</FilePathLength> 
#       <FilePath>%OSDRIVE%\USERS\THOR\DOCUMENTS\WINDOWSPOWERSHELL\SCRIPTS\GET-STUFF.PS1</FilePath> 
#       <FileHashLength>32</FileHashLength> 
#       <FileHash>EE805925E38BFAB69D49FCCE20382EB01804A8F7F9897D98D44C945861CAE9A8</FileHash> 
#       <FqbnLength>1</FqbnLength> 
#       <Fqbn>-</Fqbn> 
#       <TargetLogonId>0x3b3057</TargetLogonId> 
#       <FullFilePathLength>64</FullFilePathLength> 
#       <FullFilePath>C:\Users\<USER>\Documents\WindowsPowerShell\Scripts\get-stuff.ps1</FullFilePath> 
#     </RuleAndFileData>
#   </UserData>
# </Event>