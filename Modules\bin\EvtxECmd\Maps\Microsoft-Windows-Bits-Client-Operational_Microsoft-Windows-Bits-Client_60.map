Author: <PERSON>
Description: BITS transfer has stopped
EventId: 60
Channel: Microsoft-Windows-Bits-Client/Operational
Provider: Microsoft-Windows-Bits-Client
Maps:
   -
      Property: PayloadData1
      PropertyValue: "jobTitle: %jobTitle%"
      Values:
         -
            Name: jobTitle
            Value: "/Event/EventData/Data[@Name=\"name\"]"
   -
      Property: PayloadData2
      PropertyValue: "jobId: %jobId%"
      Values:
         -
            Name: jobId
            Value: "/Event/EventData/Data[@Name=\"Id\"]"
   -
      Property: PayloadData3
      PropertyValue: "URL: %url%"
      Values:
         -
            Name: url
            Value: "/Event/EventData/Data[@Name=\"url\"]"
   -
      Property: PayloadData4
      PropertyValue: "Peer: %peer%"
      Values:
         -
            Name: peer
            Value: "/Event/EventData/Data[@Name=\"peer\"]"
   -
      Property: PayloadData5
      PropertyValue: "Total Bytes: %bytesTotal% (Transferred: %bytesTransferred%)"
      Values:
         -
            Name: bytesTotal
            Value: "/Event/EventData/Data[@Name=\"bytesTotal\"]"
         -
            Name: bytesTransferred
            Value: "/Event/EventData/Data[@Name=\"bytesTransferred\"]"
   -
      Property: PayloadData6
      PropertyValue: "Bytes Transferred from Peer: %bytesTransferredFromPeer%"
      Values:
         -
            Name: bytesTransferredFromPeer
            Value: "/Event/EventData/Data[@Name=\"bytesTransferredFromPeer\"]"

# Documentation:
# https://kb.eventtracker.com/evtpass/evtpages/EventId_60_Microsoft-Windows-Bits-Client_64110.asp
# https://jpcertcc.github.io/ToolAnalysisResultSheet/details/BITS.htm
# https://www.sans.org/reading-room/whitepapers/forensics/bits-forensics-39195
# https://docs.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2008-r2-and-2008/cc734695(v=ws.10)
# https://www.adash.org/jfb/Training247/Bit_Deep_Dive_Tshoot.htm
# https://docs.microsoft.com/de-de/security-updates/windowsupdateservices/18127392
#
# Example Event Data:
# <Event>
#   <System>
#     <Provider Name="Microsoft-Windows-Bits-Client" Guid="ef1ab15b-46c1-414e-bb95-e76b077bd51e" />
#     <EventID>60</EventID>
#     <Version>1</Version>
#     <Level>4</Level>
#     <Task>0</Task>
#     <Opcode>2</Opcode>
#     <Keywords>0x4000000800000000</Keywords>
#     <TimeCreated SystemTime="2020-11-30 17:31:11.2022221" />
#     <EventRecordID>1532</EventRecordID>
#     <Correlation ActivityID="76099896-f8ef-40f3-853b-9d3725e4b2f7" />
#     <Execution ProcessID="7788" ThreadID="16396" />
#     <Channel>Microsoft-Windows-Bits-Client/Operational</Channel>
#     <Computer>HOSTNAME</Computer>
#     <Security UserID="S-1-5-18" />
#   </System>
#   <EventData>
#     <Data Name="transferId">76052606-f8ef-40f3-853b-9d3725e4b2f7</Data>
#     <Data Name="name">UpdateXml</Data>
#     <Data Name="Id">f4ecc13b-4421-48a3-8766-4b987a0e5995</Data>
#     <Data Name="url">https://g.live.com/123rewlive5skydrive/OneDriveProduction?OneDriveUpdate=f37fd774d9b58ea48d76eacfee1e</Data>
#     <Data Name="peer"></Data>
#     <Data Name="hr">0</Data>
#     <Data Name="fileTime">2020-11-23 20:04:21.0000000</Data>
#     <Data Name="fileLength">993</Data>
#     <Data Name="bytesTotal">993</Data>
#     <Data Name="bytesTransferred">993</Data>
#     <Data Name="proxy"></Data>
#     <Data Name="peerProtocolFlags">0</Data>
#     <Data Name="bytesTransferredFromPeer">0</Data>
#     <Data Name="AdditionalInfoHr">0</Data>
#     <Data Name="PeerContextInfo">0</Data>
#     <Data Name="bandwidthLimit">18446749973709551615</Data>
#     <Data Name="ignoreBandwidthLimitsOnLan">False</Data>
#   </EventData>
# </Event>
